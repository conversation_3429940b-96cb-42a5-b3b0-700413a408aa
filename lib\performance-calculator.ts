import { PlayerScanData, Parameter, Penalty, ZeroedPlayer, HallOfHeroes, PlayerPerformance } from '@/types/database'

export class PerformanceCalculator {
  private parameters: Parameter[] = []
  private penalties: Map<number, Penalty> = new Map()
  private zeroedPlayers: Map<number, ZeroedPlayer> = new Map()
  private hallOfHeroes: Map<number, HallOfHeroes> = new Map()

  constructor(
    parameters: Parameter[],
    penalties: Penalty[],
    zeroedPlayers: ZeroedPlayer[],
    hallOfHeroes: HallOfHeroes[]
  ) {
    this.parameters = parameters.sort((a, b) => a.power_threshold - b.power_threshold)
    
    penalties.forEach(p => this.penalties.set(p.player_id, p))
    zeroedPlayers.forEach(z => this.zeroedPlayers.set(z.player_id, z))
    hallOfHeroes.forEach(h => this.hallOfHeroes.set(h.player_id, h))
  }

  calculatePlayerPerformance(
    currentData: PlayerScanData,
    baselineData: PlayerScanData | null,
    rank: number
  ): PlayerPerformance {
    const isNewPlayer = !baselineData
    const penalty = this.penalties.get(currentData.player_id)
    const zeroedStatus = this.zeroedPlayers.get(currentData.player_id)
    const hohData = this.hallOfHeroes.get(currentData.player_id)

    // Basic calculations
    const powerBaseline = baselineData?.power || 0
    const powerCurrent = currentData.power
    const powerChange = powerCurrent - powerBaseline

    const killpointsBaseline = baselineData?.killpoints || 0
    const killpointsCurrent = currentData.killpoints
    let killpointsGain = isNewPlayer ? 0 : killpointsCurrent - killpointsBaseline

    const deadsBaseline = baselineData?.deads || 0
    const deadsCurrent = currentData.deads
    let deadsGain = isNewPlayer ? 0 : deadsCurrent - deadsBaseline

    // Apply penalties
    const kpPenaltyPercent = penalty?.kp_penalty_percent || 0
    const deadPenaltyPercent = penalty?.dead_penalty_percent || 0
    
    killpointsGain *= (1 - kpPenaltyPercent / 100)
    deadsGain *= (1 - deadPenaltyPercent / 100)

    // Apply zeroed penalty (90% reduction)
    if (zeroedStatus?.is_zeroed) {
      deadsGain *= 0.1
    }

    // Calculate tier kills gain
    const t1KillsGain = (currentData.t1_kills || 0) - (baselineData?.t1_kills || 0)
    const t2KillsGain = (currentData.t2_kills || 0) - (baselineData?.t2_kills || 0)
    const t3KillsGain = (currentData.t3_kills || 0) - (baselineData?.t3_kills || 0)
    const t4KillsGain = (currentData.t4_kills || 0) - (baselineData?.t4_kills || 0)
    const t5KillsGain = (currentData.t5_kills || 0) - (baselineData?.t5_kills || 0)
    const totalKillsGain = t1KillsGain + t2KillsGain + t3KillsGain + t4KillsGain + t5KillsGain

    // Calculate targets based on power
    const targets = this.calculateTargets(powerBaseline, isNewPlayer)
    
    // Performance ratio
    const performanceRatio = deadsGain === 0 
      ? (killpointsGain > 0 ? "∞" : "No Deaths")
      : Math.round((killpointsGain / deadsGain) * 100) / 100

    // Status determination
    const status = this.determineStatus(killpointsGain, penalty, zeroedStatus)

    // Efficiency score
    const efficiencyScore = this.calculateEfficiencyScore(killpointsGain, deadsGain)

    // Target achievement percentages
    const kpPercentAchieved = isNewPlayer || targets.kpTarget === 0 
      ? 0 
      : Math.round((killpointsGain / targets.kpTarget) * 10) / 10

    const deadsPercentAchieved = isNewPlayer || targets.deadsTarget === 0 
      ? 0 
      : Math.round((deadsGain / targets.deadsTarget) * 10) / 10

    // Hall of Heroes calculations
    let totalT4Deads = 0
    let totalT5Deads = 0
    let deadPointsEarned = 0

    if (hohData) {
      totalT4Deads = (hohData.t4_infantry + hohData.t4_cavalry + hohData.t4_archers + hohData.t4_siege)
      totalT5Deads = (hohData.t5_infantry + hohData.t5_cavalry + hohData.t5_archers + hohData.t5_siege)
      
      // Apply same penalties as regular deads
      if (zeroedStatus?.is_zeroed) {
        totalT4Deads *= 0.1
        totalT5Deads *= 0.1
      }
      totalT4Deads *= (1 - deadPenaltyPercent / 100)
      totalT5Deads *= (1 - deadPenaltyPercent / 100)

      deadPointsEarned = (totalT4Deads * 200) + (totalT5Deads * 500)
    }

    return {
      rank,
      player_id: currentData.player_id,
      name: currentData.players?.name || 'Unknown',
      alliance: currentData.alliance,
      power_baseline: powerBaseline,
      power_current: powerCurrent,
      power_change: powerChange,
      killpoints_baseline: killpointsBaseline,
      killpoints_current: killpointsCurrent,
      killpoints_gain: Math.round(killpointsGain),
      deads_baseline: deadsBaseline,
      deads_current: deadsCurrent,
      deads_gain: Math.round(deadsGain),
      total_kills_gain: totalKillsGain,
      performance_ratio: performanceRatio,
      status,
      efficiency_score: efficiencyScore,
      kp_target: targets.kpTarget,
      deads_target: targets.deadsTarget,
      kp_percent_achieved: kpPercentAchieved,
      deads_percent_achieved: deadsPercentAchieved,
      is_new_player: isNewPlayer,
      is_zeroed: zeroedStatus?.is_zeroed || false,
      is_penalized: penalty?.is_active || false,
      total_t4_deads: Math.round(totalT4Deads),
      total_t5_deads: Math.round(totalT5Deads),
      dead_points_earned: Math.round(deadPointsEarned)
    }
  }

  private calculateTargets(power: number, isNewPlayer: boolean) {
    if (isNewPlayer) {
      return { kpTarget: 0, deadsTarget: 0 }
    }

    // Find the appropriate parameter tier
    let applicableParam = this.parameters[0]
    for (const param of this.parameters) {
      if (power >= param.power_threshold) {
        applicableParam = param
      } else {
        break
      }
    }

    const kpTarget = Math.round(power * (applicableParam.kp_ratio / 100))
    const deadsTarget = Math.round(power * (applicableParam.dp_ratio / 100) / 10)

    return { kpTarget, deadsTarget }
  }

  private determineStatus(killpointsGain: number, penalty?: Penalty, zeroedStatus?: ZeroedPlayer): string {
    if (penalty?.is_active) return "🚨 PENALIZED"
    if (zeroedStatus?.is_zeroed) return "⚠️ ZEROED"
    if (killpointsGain >= 10000000) return "🔥 Elite"
    if (killpointsGain >= 5000000) return "⭐ Top Performer"
    if (killpointsGain >= 1000000) return "⚡ Active Fighter"
    if (killpointsGain >= 100000) return "🎯 Participant"
    if (killpointsGain > 0) return "🟢 Active"
    return "💤 Inactive"
  }

  private calculateEfficiencyScore(killpointsGain: number, deadsGain: number): number {
    if (killpointsGain > 0 && deadsGain > 0) {
      return Math.round((killpointsGain / 1000000) / (deadsGain / 1000000) * 100 * 10) / 10
    }
    if (killpointsGain > 0) return 100
    return 0
  }
}
