import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatNumber(num: number): string {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1) + 'B'
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

export function formatPercentage(num: number): string {
  return `${num.toFixed(1)}%`
}

export function formatDate(date: string | Date): string {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export function getStatusColor(status: string): string {
  switch (status) {
    case '🚨 PENALIZED':
      return 'text-red-600 bg-red-50'
    case '⚠️ ZEROED':
      return 'text-orange-600 bg-orange-50'
    case '🔥 Elite':
      return 'text-purple-600 bg-purple-50'
    case '⭐ Top Performer':
      return 'text-blue-600 bg-blue-50'
    case '⚡ Active Fighter':
      return 'text-green-600 bg-green-50'
    case '🎯 Participant':
      return 'text-yellow-600 bg-yellow-50'
    case '🟢 Active':
      return 'text-emerald-600 bg-emerald-50'
    case '💤 Inactive':
      return 'text-gray-600 bg-gray-50'
    default:
      return 'text-gray-600 bg-gray-50'
  }
}

export function getEfficiencyColor(score: number): string {
  if (score >= 80) return 'text-green-600'
  if (score >= 60) return 'text-yellow-600'
  if (score >= 40) return 'text-orange-600'
  return 'text-red-600'
}

export function getTargetAchievementColor(percentage: number): string {
  if (percentage >= 100) return 'text-green-600'
  if (percentage >= 75) return 'text-yellow-600'
  if (percentage >= 50) return 'text-orange-600'
  return 'text-red-600'
}
