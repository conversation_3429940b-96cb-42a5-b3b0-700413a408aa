{"dependencies": {"@supabase/supabase-js": "^2.39.0", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.4.2", "xlsx": "^0.18.5", "papaparse": "^5.4.1", "recharts": "^2.8.0", "lucide-react": "^0.294.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-progress": "^1.0.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "react-dropzone": "^14.2.3", "date-fns": "^2.30.0"}, "devDependencies": {"@types/papaparse": "^5.3.14"}}