'use client'

import React from 'react'
import { <PERSON>, Zap, Star, Trophy, Sword, Skull, Bar<PERSON>hart3, TrendingUp } from 'lucide-react'
import { DashboardMetrics } from '@/types/database'
import { formatNumber, formatDate } from '@/lib/utils'

interface DashboardMetricsProps {
  metrics: DashboardMetrics
  isLoading?: boolean
}

export function DashboardMetricsComponent({ metrics, isLoading }: DashboardMetricsProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="bg-white p-6 rounded-lg shadow animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    )
  }

  const metricCards = [
    {
      title: 'Total Governors',
      value: formatNumber(metrics.total_governors),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Active Participants',
      value: formatNumber(metrics.active_participants),
      icon: Zap,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Top Performers',
      value: formatNumber(metrics.top_performers),
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50'
    },
    {
      title: 'Elite Players',
      value: formatNumber(metrics.elite_players),
      icon: Trophy,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'Total Killpoints',
      value: formatNumber(metrics.total_killpoints),
      icon: Sword,
      color: 'text-red-600',
      bgColor: 'bg-red-50'
    },
    {
      title: 'Total Deaths',
      value: formatNumber(metrics.total_deads),
      icon: Skull,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50'
    },
    {
      title: 'Avg KP per Player',
      value: formatNumber(metrics.avg_kp_per_player),
      icon: BarChart3,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50'
    },
    {
      title: 'Avg Performance Ratio',
      value: metrics.avg_performance_ratio.toFixed(2),
      icon: TrendingUp,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header with scan info */}
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">KVK Dashboard</h2>
            <p className="text-gray-600 mt-1">
              Current Scan: <span className="font-medium">{metrics.current_scan}</span>
            </p>
          </div>
          <div className="mt-4 sm:mt-0 text-sm text-gray-500">
            Last Updated: {formatDate(metrics.last_updated)}
          </div>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metricCards.map((metric, index) => {
          const Icon = metric.icon
          return (
            <div key={index} className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${metric.bgColor}`}>
                  <Icon className={`h-6 w-6 ${metric.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Quick Stats */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Statistics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {((metrics.active_participants / metrics.total_governors) * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">Participation Rate</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {((metrics.elite_players / metrics.total_governors) * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">Elite Rate</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {metrics.total_deads > 0 ? (metrics.total_killpoints / metrics.total_deads).toFixed(2) : '∞'}
            </div>
            <div className="text-sm text-gray-600">Kingdom Performance Ratio</div>
          </div>
        </div>
      </div>
    </div>
  )
}
