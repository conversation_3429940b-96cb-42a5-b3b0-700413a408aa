# KVK Dashboard

A comprehensive Kingdom vs Kingdom (KVK) performance tracking and analytics dashboard built with Next.js, TypeScript, and Supabase.

## Features

### 📊 Core Dashboard
- Real-time KVK performance metrics
- Player rankings and statistics
- Alliance performance tracking
- Automated target calculations based on power levels

### 📁 Data Import System
- **Excel/CSV/JSON Support**: Upload scan data in multiple formats
- **Automatic Processing**: Smart data validation and processing
- **Multiple Data Types**: 
  - Scan data (player performance)
  - Parameters (power-based targets)
  - Hall of Heroes data (detailed kill/death breakdowns)
  - Penalties and zeroed player status

### 🎯 Performance Tracking
- **Baseline Comparisons**: Track progress from baseline scans
- **Target Achievement**: Power-based KP and death targets
- **Efficiency Scoring**: Performance ratios and efficiency metrics
- **Status Management**: Elite, top performer, active fighter classifications

### 🏰 Alliance Management
- Alliance member tracking
- Collective performance metrics
- Alliance-based filtering and analysis

### 🤖 Discord Integration
- Hall of Heroes data collection via Discord bot
- Service account integration for automated updates
- Detailed tier-based kill/death tracking

### ⚙️ Administrative Features
- Penalty management system
- Zeroed player tracking (90% penalty application)
- Account linking (main + farm accounts)
- Parameter configuration

## Technology Stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL + Auth)
- **File Processing**: XLSX, Papa Parse for CSV/JSON
- **UI Components**: Radix UI, Lucide React icons
- **Charts**: Recharts for data visualization

## Getting Started

### Prerequisites
- Node.js 18+ 
- Supabase account
- Discord bot (for Hall of Heroes integration)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd kvk-dashboard
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase**
   - Create a new Supabase project
   - Run the database schema from `database-schema.sql`
   - Configure Row Level Security (RLS) policies

4. **Environment Configuration**
   ```bash
   cp .env.local.example .env.local
   ```
   Fill in your Supabase and Discord configuration values.

5. **Run the development server**
   ```bash
   npm run dev
   ```

### Database Setup

Execute the SQL schema in your Supabase SQL editor:

```sql
-- Run the contents of database-schema.sql
```

### Discord Bot Setup (Optional)

For Hall of Heroes integration:

1. Create a Discord application and bot
2. Add the bot to your server with appropriate permissions
3. Configure the bot to collect kill/death data
4. Set up service account for automated data sync

## Usage

### Importing Data

1. **Navigate to Import Page**: Click "Import Data" from the dashboard
2. **Select Data Type**: Choose from scan data, parameters, Hall of Heroes, etc.
3. **Upload File**: Drag and drop or select your Excel/CSV/JSON file
4. **Preview and Confirm**: Review the data preview and confirm import
5. **Automatic Processing**: Data is validated and imported automatically

### Data Formats

#### Scan Data Format
Expected columns in Excel/CSV:
- Column A: Player ID
- Column B: Player Name  
- Column C: Power
- Column D: Killpoints
- Column E: Deaths
- Columns F-J: T1-T5 Kills
- Column Q: Alliance

#### Parameters Format
- Column A: Power Threshold
- Column B: KP Ratio (%)
- Column C: Death Points Ratio (%)

### Dashboard Features

- **Real-time Metrics**: Total governors, active participants, elite players
- **Performance Table**: Sortable and filterable player performance data
- **Target Tracking**: Visual indicators for target achievement
- **Status Management**: Automated status classification
- **Alliance Views**: Alliance-based performance analysis

## System Architecture

### Data Flow
1. **File Upload** → File Processing → Data Validation
2. **Database Storage** → Performance Calculation → Dashboard Display
3. **Discord Bot** → Hall of Heroes Data → Automated Sync

### Performance Calculations
- **Killpoints Gain**: Current KP - Baseline KP (with penalties)
- **Deaths Gain**: Current Deaths - Baseline Deaths (with zeroed/penalty modifiers)
- **Performance Ratio**: KP Gain / Deaths Gain
- **Efficiency Score**: Weighted performance calculation
- **Target Achievement**: Percentage of power-based targets met

### Penalty System
- **KP Penalties**: Percentage reduction in killpoint gains
- **Death Penalties**: Percentage reduction in death counts
- **Zeroed Status**: 90% reduction in death calculations
- **Resource Fines**: Food, wood, stone, gold penalties

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the example data formats

## Roadmap

- [ ] Advanced analytics and reporting
- [ ] Mobile app companion
- [ ] API endpoints for external integrations
- [ ] Advanced Discord bot features
- [ ] Multi-kingdom support
- [ ] Historical trend analysis
