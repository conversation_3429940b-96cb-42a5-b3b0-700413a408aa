-- KVK Dashboard Database Schema
-- Designed for Supabase PostgreSQL

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Players table (core player data)
CREATE TABLE players (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    player_id BIGINT UNIQUE NOT NULL, -- Game player ID
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    alliance VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Scans table (each scan/data import)
CREATE TABLE scans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scan_number INTEGER NOT NULL,
    scan_name VA<PERSON>HAR(255) NOT NULL, -- e.g., "Baseline", "Scan 1", "Scan 2"
    is_baseline BOOLEAN DEFAULT FALSE,
    is_latest BOOLEAN DEFAULT FALSE,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    uploaded_by UUID REFERENCES auth.users(id)
);

-- Player scan data (performance data for each scan)
CREATE TABLE player_scan_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scan_id UUID REFERENCES scans(id) ON DELETE CASCADE,
    player_id BIGINT REFERENCES players(player_id) ON DELETE CASCADE,
    power BIGINT DEFAULT 0,
    killpoints BIGINT DEFAULT 0,
    deads BIGINT DEFAULT 0,
    t1_kills BIGINT DEFAULT 0,
    t2_kills BIGINT DEFAULT 0,
    t3_kills BIGINT DEFAULT 0,
    t4_kills BIGINT DEFAULT 0,
    t5_kills BIGINT DEFAULT 0,
    alliance VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(scan_id, player_id)
);

-- Parameters table (power-based targets)
CREATE TABLE parameters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    power_threshold BIGINT NOT NULL,
    kp_ratio DECIMAL(5,2) NOT NULL, -- Percentage as decimal (37.50 = 37.50%)
    dp_ratio DECIMAL(5,2) NOT NULL, -- Death points ratio
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(power_threshold)
);

-- Penalties table
CREATE TABLE penalties (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    player_id BIGINT REFERENCES players(player_id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT TRUE,
    kp_penalty_percent DECIMAL(5,2) DEFAULT 0,
    dead_penalty_percent DECIMAL(5,2) DEFAULT 0,
    food_fine BIGINT DEFAULT 0,
    wood_fine BIGINT DEFAULT 0,
    stone_fine BIGINT DEFAULT 0,
    gold_fine BIGINT DEFAULT 0,
    reason TEXT,
    date_applied TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    applied_by UUID REFERENCES auth.users(id),
    UNIQUE(player_id)
);

-- Zeroed status table
CREATE TABLE zeroed_players (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    player_id BIGINT REFERENCES players(player_id) ON DELETE CASCADE,
    is_zeroed BOOLEAN DEFAULT FALSE,
    date_zeroed TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    updated_by UUID REFERENCES auth.users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(player_id)
);

-- Hall of Heroes data (from Discord bot)
CREATE TABLE hall_of_heroes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    player_id BIGINT REFERENCES players(player_id) ON DELETE CASCADE,
    t1_infantry BIGINT DEFAULT 0,
    t1_cavalry BIGINT DEFAULT 0,
    t1_archers BIGINT DEFAULT 0,
    t1_siege BIGINT DEFAULT 0,
    t2_infantry BIGINT DEFAULT 0,
    t2_cavalry BIGINT DEFAULT 0,
    t2_archers BIGINT DEFAULT 0,
    t2_siege BIGINT DEFAULT 0,
    t3_infantry BIGINT DEFAULT 0,
    t3_cavalry BIGINT DEFAULT 0,
    t3_archers BIGINT DEFAULT 0,
    t3_siege BIGINT DEFAULT 0,
    t4_infantry BIGINT DEFAULT 0,
    t4_cavalry BIGINT DEFAULT 0,
    t4_archers BIGINT DEFAULT 0,
    t4_siege BIGINT DEFAULT 0,
    t5_infantry BIGINT DEFAULT 0,
    t5_cavalry BIGINT DEFAULT 0,
    t5_archers BIGINT DEFAULT 0,
    t5_siege BIGINT DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(player_id)
);

-- Account links (main + farm accounts)
CREATE TABLE account_links (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    main_account_id BIGINT REFERENCES players(player_id) ON DELETE CASCADE,
    main_account_name VARCHAR(255),
    farm1_id BIGINT REFERENCES players(player_id) ON DELETE SET NULL,
    farm1_name VARCHAR(255),
    farm2_id BIGINT REFERENCES players(player_id) ON DELETE SET NULL,
    farm2_name VARCHAR(255),
    farm3_id BIGINT REFERENCES players(player_id) ON DELETE SET NULL,
    farm3_name VARCHAR(255),
    farm4_id BIGINT REFERENCES players(player_id) ON DELETE SET NULL,
    farm4_name VARCHAR(255),
    farm5_id BIGINT REFERENCES players(player_id) ON DELETE SET NULL,
    farm5_name VARCHAR(255),
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(main_account_id)
);

-- Indexes for performance
CREATE INDEX idx_players_player_id ON players(player_id);
CREATE INDEX idx_player_scan_data_scan_id ON player_scan_data(scan_id);
CREATE INDEX idx_player_scan_data_player_id ON player_scan_data(player_id);
CREATE INDEX idx_scans_is_latest ON scans(is_latest);
CREATE INDEX idx_scans_is_baseline ON scans(is_baseline);
CREATE INDEX idx_penalties_player_id ON penalties(player_id);
CREATE INDEX idx_penalties_is_active ON penalties(is_active);
CREATE INDEX idx_zeroed_players_player_id ON zeroed_players(player_id);
CREATE INDEX idx_hall_of_heroes_player_id ON hall_of_heroes(player_id);

-- RLS (Row Level Security) policies will be added after Supabase setup
