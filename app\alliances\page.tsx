'use client'

import React, { useState, useEffect } from 'react'
import { DatabaseService } from '@/lib/supabase'
import { PerformanceCalculator } from '@/lib/performance-calculator'
import { AlliancePerformance, PlayerPerformance } from '@/types/database'
import { formatNumber, formatDate } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Users, Crown, Sword, Skull, TrendingUp, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function AlliancesPage() {
  const [allianceData, setAllianceData] = useState<AlliancePerformance[]>([])
  const [selectedAlliance, setSelectedAlliance] = useState<string | null>(null)
  const [allianceMembers, setAllianceMembers] = useState<PlayerPerformance[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadAllianceData()
  }, [])

  const loadAllianceData = async () => {
    setIsLoading(true)
    try {
      const db = new DatabaseService()
      
      // Get latest scan data
      const latestScan = await db.getLatestScan()
      const baselineScan = await db.getBaselineScan()
      
      if (!latestScan) {
        setIsLoading(false)
        return
      }

      // Get all data needed for calculations
      const [currentScanData, baselineScanData, parameters, penalties, zeroedPlayers, hallOfHeroes] = await Promise.all([
        db.getPlayerScanData(latestScan.id),
        baselineScan ? db.getPlayerScanData(baselineScan.id) : Promise.resolve([]),
        db.getParameters(),
        db.getPenalties(),
        db.getZeroedPlayers(),
        db.getHallOfHeroes()
      ])

      // Calculate performance data
      const calculator = new PerformanceCalculator(parameters, penalties, zeroedPlayers, hallOfHeroes)
      
      const allPerformance: PlayerPerformance[] = currentScanData.map((current, index) => {
        const baseline = baselineScanData.find(b => b.player_id === current.player_id)
        return calculator.calculatePlayerPerformance(current, baseline || null, index + 1)
      })

      // Group by alliance
      const allianceGroups = allPerformance.reduce((groups, player) => {
        const alliance = player.alliance || 'No Alliance'
        if (!groups[alliance]) {
          groups[alliance] = []
        }
        groups[alliance].push(player)
        return groups
      }, {} as Record<string, PlayerPerformance[]>)

      // Calculate alliance metrics
      const allianceMetrics: AlliancePerformance[] = Object.entries(allianceGroups).map(([alliance, members]) => {
        const totalKP = members.reduce((sum, m) => sum + m.killpoints_gain, 0)
        const totalDeads = members.reduce((sum, m) => sum + m.deads_gain, 0)
        const avgKP = members.length > 0 ? totalKP / members.length : 0
        const performanceRatio = totalDeads > 0 ? totalKP / totalDeads : totalKP > 0 ? Infinity : 0

        return {
          alliance,
          members: members.length,
          total_kp_gained: totalKP,
          avg_kp_per_member: avgKP,
          total_deads: totalDeads,
          performance_ratio: performanceRatio
        }
      })

      // Sort by total KP gained
      allianceMetrics.sort((a, b) => b.total_kp_gained - a.total_kp_gained)

      setAllianceData(allianceMetrics)
      
      // Store all performance data for member details
      if (selectedAlliance) {
        const members = allianceGroups[selectedAlliance] || []
        members.sort((a, b) => b.killpoints_gain - a.killpoints_gain)
        setAllianceMembers(members)
      }
    } catch (error) {
      console.error('Failed to load alliance data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAllianceSelect = async (alliance: string) => {
    setSelectedAlliance(alliance)
    
    // Load member details
    const db = new DatabaseService()
    const latestScan = await db.getLatestScan()
    const baselineScan = await db.getBaselineScan()
    
    if (!latestScan) return

    const [currentScanData, baselineScanData, parameters, penalties, zeroedPlayers, hallOfHeroes] = await Promise.all([
      db.getPlayerScanData(latestScan.id),
      baselineScan ? db.getPlayerScanData(baselineScan.id) : Promise.resolve([]),
      db.getParameters(),
      db.getPenalties(),
      db.getZeroedPlayers(),
      db.getHallOfHeroes()
    ])

    const calculator = new PerformanceCalculator(parameters, penalties, zeroedPlayers, hallOfHeroes)
    
    const allianceMembers = currentScanData
      .filter(player => (player.alliance || 'No Alliance') === alliance)
      .map((current, index) => {
        const baseline = baselineScanData.find(b => b.player_id === current.player_id)
        return calculator.calculatePlayerPerformance(current, baseline || null, index + 1)
      })
      .sort((a, b) => b.killpoints_gain - a.killpoints_gain)

    setAllianceMembers(allianceMembers)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading alliance data...</p>
        </div>
      </div>
    )
  }

  if (selectedAlliance) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between py-6">
              <div className="flex items-center">
                <Button
                  variant="outline"
                  onClick={() => setSelectedAlliance(null)}
                  className="mr-4"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Alliances
                </Button>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">{selectedAlliance}</h1>
                  <p className="text-gray-600">{allianceMembers.length} members</p>
                </div>
              </div>
              <Link href="/">
                <Button variant="outline">
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Alliance Summary */}
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Alliance Summary</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {formatNumber(allianceMembers.reduce((sum, m) => sum + m.killpoints_gain, 0))}
                </div>
                <div className="text-sm text-gray-600">Total KP Gained</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {formatNumber(allianceMembers.reduce((sum, m) => sum + m.deads_gain, 0))}
                </div>
                <div className="text-sm text-gray-600">Total Deaths</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {formatNumber(allianceMembers.reduce((sum, m) => sum + m.killpoints_gain, 0) / allianceMembers.length)}
                </div>
                <div className="text-sm text-gray-600">Avg KP per Member</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {allianceMembers.filter(m => m.killpoints_gain >= 10000000).length}
                </div>
                <div className="text-sm text-gray-600">Elite Players</div>
              </div>
            </div>
          </div>

          {/* Members Table */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Alliance Members</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rank
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Player
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Power
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      KP Gain
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Deaths
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ratio
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {allianceMembers.map((member, index) => (
                    <tr key={member.player_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {index + 1}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{member.name}</div>
                          <div className="text-sm text-gray-500">ID: {member.player_id}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatNumber(member.power_current)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatNumber(member.killpoints_gain)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatNumber(member.deads_gain)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {typeof member.performance_ratio === 'number' 
                          ? member.performance_ratio.toFixed(2)
                          : member.performance_ratio
                        }
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                          {member.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Alliance Performance</h1>
              <p className="text-gray-600">Compare alliance performance and member contributions</p>
            </div>
            <Link href="/">
              <Button variant="outline">
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Alliance Rankings</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Alliance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Members
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total KP Gained
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Avg KP per Member
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Deaths
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Performance Ratio
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {allianceData.map((alliance, index) => (
                  <tr key={alliance.alliance} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {index + 1}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Crown className="h-4 w-4 text-yellow-500 mr-2" />
                        <span className="text-sm font-medium text-gray-900">{alliance.alliance}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center">
                        <Users className="h-4 w-4 text-gray-400 mr-1" />
                        {alliance.members}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center">
                        <Sword className="h-4 w-4 text-red-500 mr-1" />
                        {formatNumber(alliance.total_kp_gained)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatNumber(alliance.avg_kp_per_member)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center">
                        <Skull className="h-4 w-4 text-gray-500 mr-1" />
                        {formatNumber(alliance.total_deads)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center">
                        <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                        {isFinite(alliance.performance_ratio) 
                          ? alliance.performance_ratio.toFixed(2)
                          : '∞'
                        }
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleAllianceSelect(alliance.alliance)}
                      >
                        View Members
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
