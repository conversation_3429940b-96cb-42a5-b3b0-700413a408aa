'use client'

import React, { useState } from 'react'
import { FileUpload, FilePreview } from '@/components/file-upload'
import { Button } from '@/components/ui/button'
import { FileProcessor } from '@/lib/file-processor'
import { DatabaseService } from '@/lib/supabase'
import { AlertCircle, CheckCircle, Upload, Database, Settings, Users } from 'lucide-react'

type ImportType = 'scan' | 'parameters' | 'hall-of-heroes' | 'penalties' | 'zeroed'

export default function ImportPage() {
  const [selectedType, setSelectedType] = useState<ImportType>('scan')
  const [previewData, setPreviewData] = useState<any[] | null>(null)
  const [fileName, setFileName] = useState('')
  const [isImporting, setIsImporting] = useState(false)
  const [importResult, setImportResult] = useState<{ success: boolean; message: string; count?: number } | null>(null)

  const importTypes = [
    {
      id: 'scan' as ImportType,
      title: 'Scan Data',
      description: 'Import player performance data from scans',
      icon: Database,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      id: 'parameters' as ImportType,
      title: 'Parameters',
      description: 'Import power-based target parameters',
      icon: Settings,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      id: 'hall-of-heroes' as ImportType,
      title: 'Hall of Heroes',
      description: 'Import detailed kill/death data from Discord bot',
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      id: 'penalties' as ImportType,
      title: 'Penalties',
      description: 'Import player penalty data',
      icon: AlertCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50'
    },
    {
      id: 'zeroed' as ImportType,
      title: 'Zeroed Players',
      description: 'Import zeroed player status',
      icon: AlertCircle,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    }
  ]

  const handleFileProcessed = (data: any[], fileName: string, fileType: string) => {
    setPreviewData(data)
    setFileName(fileName)
    setImportResult(null)
  }

  const handleImport = async () => {
    if (!previewData) return

    setIsImporting(true)
    setImportResult(null)

    try {
      const db = new DatabaseService()
      let result

      switch (selectedType) {
        case 'scan':
          result = await importScanData(previewData, fileName, db)
          break
        case 'parameters':
          result = await importParametersData(previewData, db)
          break
        case 'hall-of-heroes':
          result = await importHallOfHeroesData(previewData, db)
          break
        case 'penalties':
          result = await importPenaltiesData(previewData, db)
          break
        case 'zeroed':
          result = await importZeroedData(previewData, db)
          break
        default:
          throw new Error('Invalid import type')
      }

      setImportResult(result)
      if (result.success) {
        setPreviewData(null)
        setFileName('')
      }
    } catch (error) {
      setImportResult({
        success: false,
        message: error instanceof Error ? error.message : 'Import failed'
      })
    } finally {
      setIsImporting(false)
    }
  }

  const importScanData = async (data: any[], fileName: string, db: DatabaseService) => {
    const scanName = fileName.replace(/\.(xlsx|xls|csv|json)$/i, '')
    const processedData = FileProcessor.processScanData(data, scanName)
    const validation = FileProcessor.validateScanData(processedData)

    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`)
    }

    // Create scan record
    const scan = await db.createScan({
      scan_number: processedData.scanNumber,
      scan_name: processedData.scanName,
      is_baseline: processedData.scanName.toLowerCase().includes('baseline')
    })

    // Upsert players
    for (const player of processedData.players) {
      await db.upsertPlayer({
        player_id: player.player_id,
        name: player.name,
        alliance: player.alliance
      })
    }

    // Insert scan data
    const scanDataRecords = processedData.players.map(player => ({
      scan_id: scan.id,
      player_id: player.player_id,
      power: player.power,
      killpoints: player.killpoints,
      deads: player.deads,
      t1_kills: player.t1_kills || 0,
      t2_kills: player.t2_kills || 0,
      t3_kills: player.t3_kills || 0,
      t4_kills: player.t4_kills || 0,
      t5_kills: player.t5_kills || 0,
      alliance: player.alliance
    }))

    await db.upsertPlayerScanData(scanDataRecords)

    return {
      success: true,
      message: `Successfully imported ${processedData.players.length} players for ${processedData.scanName}`,
      count: processedData.players.length
    }
  }

  const importParametersData = async (data: any[], db: DatabaseService) => {
    const processedData = FileProcessor.processParametersData(data)
    
    if (processedData.parameters.length === 0) {
      throw new Error('No valid parameters found in file')
    }

    await db.upsertParameters(processedData.parameters)

    return {
      success: true,
      message: `Successfully imported ${processedData.parameters.length} parameter tiers`,
      count: processedData.parameters.length
    }
  }

  const importHallOfHeroesData = async (data: any[], db: DatabaseService) => {
    const processedData = FileProcessor.processHallOfHeroesData(data)
    
    if (processedData.length === 0) {
      throw new Error('No valid Hall of Heroes data found in file')
    }

    await db.upsertHallOfHeroes(processedData)

    return {
      success: true,
      message: `Successfully imported Hall of Heroes data for ${processedData.length} players`,
      count: processedData.length
    }
  }

  const importPenaltiesData = async (data: any[], db: DatabaseService) => {
    // Implementation for penalties import
    return {
      success: true,
      message: 'Penalties import not yet implemented',
      count: 0
    }
  }

  const importZeroedData = async (data: any[], db: DatabaseService) => {
    // Implementation for zeroed players import
    return {
      success: true,
      message: 'Zeroed players import not yet implemented',
      count: 0
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Data Import</h1>
          <p className="mt-2 text-gray-600">
            Upload and import your KVK data files to automatically update the dashboard
          </p>
        </div>

        {/* Import Type Selection */}
        <div className="mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Select Import Type</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {importTypes.map((type) => {
              const Icon = type.icon
              return (
                <button
                  key={type.id}
                  onClick={() => setSelectedType(type.id)}
                  className={`p-4 rounded-lg border-2 text-left transition-colors ${
                    selectedType === type.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center mb-2">
                    <div className={`p-2 rounded-lg ${type.bgColor}`}>
                      <Icon className={`h-5 w-5 ${type.color}`} />
                    </div>
                    <h3 className="ml-3 font-medium text-gray-900">{type.title}</h3>
                  </div>
                  <p className="text-sm text-gray-600">{type.description}</p>
                </button>
              )
            })}
          </div>
        </div>

        {/* File Upload */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Upload {importTypes.find(t => t.id === selectedType)?.title} File
          </h2>
          
          {!previewData ? (
            <FileUpload onFileProcessed={handleFileProcessed} />
          ) : (
            <FilePreview
              data={previewData}
              fileName={fileName}
              onConfirm={handleImport}
              onCancel={() => {
                setPreviewData(null)
                setFileName('')
                setImportResult(null)
              }}
            />
          )}
        </div>

        {/* Import Status */}
        {isImporting && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mr-3"></div>
              <span className="text-gray-900">Importing data...</span>
            </div>
          </div>
        )}

        {/* Import Result */}
        {importResult && (
          <div className={`rounded-lg shadow p-6 ${
            importResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-center">
              {importResult.success ? (
                <CheckCircle className="h-6 w-6 text-green-500 mr-3" />
              ) : (
                <AlertCircle className="h-6 w-6 text-red-500 mr-3" />
              )}
              <div>
                <p className={`font-medium ${importResult.success ? 'text-green-900' : 'text-red-900'}`}>
                  {importResult.message}
                </p>
                {importResult.count && (
                  <p className={`text-sm ${importResult.success ? 'text-green-700' : 'text-red-700'}`}>
                    {importResult.count} records processed
                  </p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
