import * as XLSX from 'xlsx'
import Papa from 'papaparse'
import { PlayerScanData, Parameter } from '@/types/database'

export interface ProcessedScanData {
  players: Array<{
    player_id: number
    name: string
    alliance?: string
    power: number
    killpoints: number
    deads: number
    t1_kills?: number
    t2_kills?: number
    t3_kills?: number
    t4_kills?: number
    t5_kills?: number
  }>
  scanName: string
  scanNumber: number
}

export interface ProcessedParametersData {
  parameters: Parameter[]
}

export class FileProcessor {
  
  static async processFile(file: File): Promise<any[]> {
    const extension = file.name.split('.').pop()?.toLowerCase()
    
    switch (extension) {
      case 'xlsx':
      case 'xls':
        return this.processExcelFile(file)
      case 'csv':
        return this.processCsvFile(file)
      case 'json':
        return this.processJsonFile(file)
      default:
        throw new Error(`Unsupported file type: ${extension}`)
    }
  }

  private static async processExcelFile(file: File): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer)
          const workbook = XLSX.read(data, { type: 'array' })
          
          // Get the first worksheet
          const firstSheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[firstSheetName]
          
          // Convert to JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
          resolve(jsonData as any[])
        } catch (error) {
          reject(error)
        }
      }
      
      reader.onerror = () => reject(new Error('Failed to read Excel file'))
      reader.readAsArrayBuffer(file)
    })
  }

  private static async processCsvFile(file: File): Promise<any[]> {
    return new Promise((resolve, reject) => {
      Papa.parse(file, {
        complete: (results) => {
          resolve(results.data as any[])
        },
        error: (error) => {
          reject(error)
        },
        skipEmptyLines: true
      })
    })
  }

  private static async processJsonFile(file: File): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const jsonData = JSON.parse(e.target?.result as string)
          resolve(Array.isArray(jsonData) ? jsonData : [jsonData])
        } catch (error) {
          reject(new Error('Invalid JSON format'))
        }
      }
      
      reader.onerror = () => reject(new Error('Failed to read JSON file'))
      reader.readAsText(file)
    })
  }

  static processScanData(rawData: any[], scanName: string): ProcessedScanData {
    // Detect scan number from name
    const scanNumber = this.extractScanNumber(scanName)
    
    // Skip header row if it exists
    const dataRows = rawData.slice(1)
    
    const players = dataRows
      .filter(row => row && row.length >= 5) // Ensure minimum required columns
      .map((row, index) => {
        try {
          return {
            player_id: this.parseNumber(row[0]), // Column A: ID
            name: String(row[1] || '').trim(), // Column B: Name
            power: this.parseNumber(row[2]), // Column C: Power
            killpoints: this.parseNumber(row[3]), // Column D: Killpoints
            deads: this.parseNumber(row[4]), // Column E: Deads
            t1_kills: this.parseNumber(row[5]), // Column F: T1 Kills
            t2_kills: this.parseNumber(row[6]), // Column G: T2 Kills
            t3_kills: this.parseNumber(row[7]), // Column H: T3 Kills
            t4_kills: this.parseNumber(row[8]), // Column I: T4 Kills
            t5_kills: this.parseNumber(row[9]), // Column J: T5 Kills
            alliance: String(row[16] || '').trim() || undefined // Column Q: Alliance
          }
        } catch (error) {
          console.warn(`Error processing row ${index + 2}:`, error)
          return null
        }
      })
      .filter((player): player is NonNullable<typeof player> => 
        player !== null && 
        player.player_id > 0 && 
        player.name.length > 0
      )

    return {
      players,
      scanName,
      scanNumber
    }
  }

  static processParametersData(rawData: any[]): ProcessedParametersData {
    // Skip header row
    const dataRows = rawData.slice(1)
    
    const parameters = dataRows
      .filter(row => row && row.length >= 3)
      .map((row, index) => {
        try {
          return {
            power_threshold: this.parseNumber(row[0]), // Column A: Power
            kp_ratio: this.parsePercentage(row[1]), // Column B: KP Ratio
            dp_ratio: this.parsePercentage(row[2]) // Column C: DP Ratio
          }
        } catch (error) {
          console.warn(`Error processing parameter row ${index + 2}:`, error)
          return null
        }
      })
      .filter((param): param is NonNullable<typeof param> => 
        param !== null && param.power_threshold > 0
      )

    return { parameters }
  }

  static processHallOfHeroesData(rawData: any[]) {
    // Skip header row
    const dataRows = rawData.slice(1)
    
    return dataRows
      .filter(row => row && row.length >= 21) // Ensure all columns present
      .map((row, index) => {
        try {
          return {
            player_id: this.parseNumber(row[0]), // Column A: Player ID
            t1_infantry: this.parseNumber(row[1]),
            t1_cavalry: this.parseNumber(row[2]),
            t1_archers: this.parseNumber(row[3]),
            t1_siege: this.parseNumber(row[4]),
            t2_infantry: this.parseNumber(row[5]),
            t2_cavalry: this.parseNumber(row[6]),
            t2_archers: this.parseNumber(row[7]),
            t2_siege: this.parseNumber(row[8]),
            t3_infantry: this.parseNumber(row[9]),
            t3_cavalry: this.parseNumber(row[10]),
            t3_archers: this.parseNumber(row[11]),
            t3_siege: this.parseNumber(row[12]),
            t4_infantry: this.parseNumber(row[13]),
            t4_cavalry: this.parseNumber(row[14]),
            t4_archers: this.parseNumber(row[15]),
            t4_siege: this.parseNumber(row[16]),
            t5_infantry: this.parseNumber(row[17]),
            t5_cavalry: this.parseNumber(row[18]),
            t5_archers: this.parseNumber(row[19]),
            t5_siege: this.parseNumber(row[20])
          }
        } catch (error) {
          console.warn(`Error processing Hall of Heroes row ${index + 2}:`, error)
          return null
        }
      })
      .filter((data): data is NonNullable<typeof data> => 
        data !== null && data.player_id > 0
      )
  }

  private static parseNumber(value: any): number {
    if (typeof value === 'number') return value
    if (typeof value === 'string') {
      // Remove commas and parse
      const cleaned = value.replace(/,/g, '')
      const parsed = parseFloat(cleaned)
      return isNaN(parsed) ? 0 : parsed
    }
    return 0
  }

  private static parsePercentage(value: any): number {
    if (typeof value === 'number') return value
    if (typeof value === 'string') {
      // Remove % sign and parse
      const cleaned = value.replace(/%/g, '')
      const parsed = parseFloat(cleaned)
      return isNaN(parsed) ? 0 : parsed
    }
    return 0
  }

  private static extractScanNumber(scanName: string): number {
    // Extract number from scan name (e.g., "Scan 1" -> 1, "Baseline" -> 0)
    if (scanName.toLowerCase().includes('baseline')) return 0
    
    const match = scanName.match(/scan\s*(\d+)/i)
    return match ? parseInt(match[1]) : 0
  }

  static validateScanData(data: ProcessedScanData): { isValid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!data.players || data.players.length === 0) {
      errors.push('No valid player data found')
    }
    
    if (!data.scanName || data.scanName.trim().length === 0) {
      errors.push('Scan name is required')
    }
    
    // Check for duplicate player IDs
    const playerIds = data.players.map(p => p.player_id)
    const duplicates = playerIds.filter((id, index) => playerIds.indexOf(id) !== index)
    if (duplicates.length > 0) {
      errors.push(`Duplicate player IDs found: ${duplicates.join(', ')}`)
    }
    
    // Validate required fields
    const invalidPlayers = data.players.filter(p => 
      !p.player_id || !p.name || p.power < 0 || p.killpoints < 0 || p.deads < 0
    )
    if (invalidPlayers.length > 0) {
      errors.push(`${invalidPlayers.length} players have invalid data`)
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
}
