##App Scripts:
function updateLatestScan() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheets = ss.getSheets();
  var perfSheet = ss.getSheetByName('Performance');
  
  // Find highest numbered scan sheet (including Scan 0)
  var maxScanNumber = -1; // Start at -1 to include Scan 0
  var latestScanName = "Baseline";
  var foundScanSheets = false;
  
  sheets.forEach(function(sheet) {
    var name = sheet.getName();
    if (name.startsWith('Scan ')) {
      foundScanSheets = true;
      var numberPart = name.replace('Scan ', '');
      var number = parseInt(numberPart);
      
      // Check if it's a valid number (including 0)
      if (!isNaN(number) && number >= 0 && number > maxScanNumber) {
        maxScanNumber = number;
        latestScanName = name;
      }
    }
  });
  
  // If no scan sheets found, stick with Baseline
  if (!foundScanSheets) {
    latestScanName = "Baseline";
  }
  
  // Update the latest scan reference
  perfSheet.getRange('W1').setValue(latestScanName);
  
  // Log update
  console.log('Latest scan updated to: ' + latestScanName);
  console.log('Found scan sheets: ' + foundScanSheets);
  console.log('Max scan number: ' + maxScanNumber);
}

##/End App Scripts

##Performance Sheet:

A1: Rank
A2:ROW()-1

B1:ID
B2:=VALUE(INDIRECT("'"&$W$1&"'!A"&ROW()))

C1:Name
C2:=IFERROR(INDIRECT("'"&$W$1&"'!B"&ROW()),"")

D1:Alliance
D2:=IFERROR(INDIRECT("'"&$W$1&"'!Q"&ROW()),"")

E1:Power_Baseline
E2:=VALUE(IFERROR(INDEX(INDIRECT("Baseline!C:C"),MATCH(B2,INDIRECT("Baseline!A:A"),0)),0))

F1:Power_Current
F2:=VALUE(IFERROR(INDIRECT("'"&$W$1&"'!C"&ROW()),0))

G1:Power_Change
G2:=VALUE(F2-E2)

H1:Killpoints_Baseline
H2:=VALUE(IFERROR(INDEX(INDIRECT("Baseline!D:D"),MATCH(B2,INDIRECT("Baseline!A:A"),0)),0))

I1:Killpoints_Current
I2:=VALUE(IFERROR(INDIRECT("'"&$W$1&"'!D"&ROW()),0))

J1:Killpoints_Gain
J2:=VALUE(IF(AB2="NEW PLAYER",0,I2-H2))*(1-AE2/100)

K1:Deads_Baseline
K2:=VALUE(IFERROR(INDEX(INDIRECT("Baseline!E:E"),MATCH(B2,INDIRECT("Baseline!A:A"),0)),0))

L1:Deads_Current
L2:=VALUE(IFERROR(INDIRECT("'"&$W$1&"'!E"&ROW()),0))

M1:Deads_Gain
M2:=VALUE(IF(AB2="NEW PLAYER",0,L2-K2))*(IF(INDEX(Zeroed!C:C,MATCH(B2,Zeroed!A:A,0))=TRUE,0.1,1))*(1-AF2/100)

N1:T1_Kills_Gain
N2:=VALUE(IFERROR(INDIRECT("'"&$W$1&"'!F"&ROW()),0)-IFERROR(INDEX(INDIRECT("Baseline!F:F"),MATCH(B2,INDIRECT("Baseline!A:A"),0)),0))

O1:T2_Kills_Gain
O2:=VALUE(IFERROR(INDIRECT("'"&$W$1&"'!G"&ROW()),0)-IFERROR(INDEX(INDIRECT("Baseline!G:G"),MATCH(B2,INDIRECT("Baseline!A:A"),0)),0))

P1:T3_Kills_Gain
P2:=VALUE(IFERROR(INDIRECT("'"&$W$1&"'!H"&ROW()),0)-IFERROR(INDEX(INDIRECT("Baseline!H:H"),MATCH(B2,INDIRECT("Baseline!A:A"),0)),0))

Q1:T4_Kills_Gain
Q2:=VALUE(IFERROR(INDIRECT("'"&$W$1&"'!I"&ROW()),0)-IFERROR(INDEX(INDIRECT("Baseline!I:I"),MATCH(B2,INDIRECT("Baseline!A:A"),0)),0))

R1:T5_Kills_Gain
R2:=VALUE(IFERROR(INDIRECT("'"&$W$1&"'!J"&ROW()),0)-IFERROR(INDEX(INDIRECT("Baseline!J:J"),MATCH(B2,INDIRECT("Baseline!A:A"),0)),0))

S1:Total_Kills_Gain
S2:=VALUE(N2+O2+P2+Q2+R2)

T1:Performance_Ratio
T2:=IF(M2=0,"No Deaths",IF(M2>0,ROUND(J2/M2,2),"∞"))

U1:Status
U2:=IF(INDEX(Penalties!C:C,MATCH(B2,Penalties!A:A,0))=TRUE,"🚨 PENALIZED",IF(INDEX(Zeroed!C:C,MATCH(B2,Zeroed!A:A,0))=TRUE,"⚠️ ZEROED",IF(J2>=10000000,"🔥 Elite",IF(J2>=5000000,"⭐ Top Performer",IF(J2>=1000000,"⚡ Active Fighter",IF(J2>=100000,"🎯 Participant",IF(J2>0,"🟢 Active","💤 Inactive")))))))

V1:Efficiency_Score
V2:=IF(AND(J2>0,M2>0),ROUND((J2/1000000)/(M2/1000000)*100,1),IF(J2>0,100,0))

X1:KP_Target
X2:=IF(AB2="NEW PLAYER",0,VALUE(E2*VLOOKUP(E2,Parameters!A:C,2,TRUE)))

Y1:Deads_Target
Y2:=IF(AB2="NEW PLAYER",0,VALUE(E2*VLOOKUP(E2,Parameters!A:C,3,TRUE)/10))

Z1:KP_Percent_Achieved
Z2:=VALUE(IF(OR(AB2="NEW PLAYER",X2=0),0,ROUND((J2/X2),1)))

AA1:Deads_Percent_Achieved
AA2:=VALUE(IF(OR(AB2="NEW PLAYER",Y2=0),0,ROUND((M2/Y2),1)))

AB1:New_Player_Flag
AB2:=IF(ISERROR(MATCH(B2,Baseline!A:A,0)),"NEW PLAYER","Existing")

AC1:Zeroed Status
AC2:=IF(INDEX(Zeroed!C:C,MATCH(B2,Zeroed!A:A,0))=TRUE,"⚠️ ZEROED (90% Penalty)","Active")

AD1:Penalty Status
AD2:=IF(INDEX(Penalties!C:C,MATCH(B2,Penalties!A:A,0))=TRUE,"🚨 PENALIZED","No Penalty")

AE1:KP Penalty %
AE2:=IF(INDEX(Penalties!C:C,MATCH(B2,Penalties!A:A,0))=TRUE,INDEX(Penalties!D:D,MATCH(B2,Penalties!A:A,0)),0)

AF1:Dead Penalty %
AF2:=IF(INDEX(Penalties!C:C,MATCH(B2,Penalties!A:A,0))=TRUE,INDEX(Penalties!E:E,MATCH(B2,Penalties!A:A,0)),0)

AG1:Total T4 Deads
AG2:=IFERROR(SUMIFS('Hall Of Heroes'!N:N,'Hall Of Heroes'!A:A,B2)+SUMIFS('Hall Of Heroes'!O:O,'Hall Of Heroes'!A:A,B2)+SUMIFS('Hall Of Heroes'!P:P,'Hall Of Heroes'!A:A,B2)+SUMIFS('Hall Of Heroes'!Q:Q,'Hall Of Heroes'!A:A,B2),0)*(IF(INDEX(Zeroed!C:C,MATCH(B2,Zeroed!A:A,0))=TRUE,0.1,1))*(1-AF2/100)

AH1:Total T5 Deads
AH2:=IFERROR(SUMIFS('Hall Of Heroes'!R:R,'Hall Of Heroes'!A:A,B2)+SUMIFS('Hall Of Heroes'!S:S,'Hall Of Heroes'!A:A,B2)+SUMIFS('Hall Of Heroes'!T:T,'Hall Of Heroes'!A:A,B2)+SUMIFS('Hall Of Heroes'!U:U,'Hall Of Heroes'!A:A,B2),0)*(IF(INDEX(Zeroed!C:C,MATCH(B2,Zeroed!A:A,0))=TRUE,0.1,1))*(1-AF2/100)

AI1:T4 Dead Required
AI2:=Y2/4

AJ1:T5 Dead Required
AJ2:=Y2/10

AK1:Target Dead Points
AK2:=Y2*500

AL1:T4 Dead Efficiency
AL2:=IF(AI2=0,"No Target",IF(AG2>=AI2,"✅",ROUND((AG2/AI2)*100,1)&"%"))

AM1:T5 Dead Efficiency
AM2:=IF(AJ2=0,"No Target",IF(AH2>=AJ2,"✅",ROUND((AH2/AJ2)*100,1)&"%"))

AN1:Dead Points Earned
AN2:=(AG2*200)+(AH2*500)

AO1:Dead Points Efficiency
AO2:=IF(AK2=0,"No Target",IF(AN2>=AK2,"✅",ROUND((AN2/AK2)*100,1)&"%"))

AP1:T4/T5 Dead Ratio
AP2:=IF(AH2=0,IF(AG2>0,"T4 Only","No T4/T5 Deaths"),ROUND(AG2/AH2,2)&":1")

AQ1:T5 Percentage
AQ2:=IF(AG2+AH2=0,"No T5 Deaths",ROUND((AH2/(AG2+AH2))*100,1)&"% T5")

AT1:KINGDOM KP
AT2:=VALUE(SUM(Performance!J:J))

AU1:KINGDOM DEADS
AU2:=VALUE(SUM(Performance!M:M))

##/End Performance Sheet

##Target Status Sheet:

A1:Both Targets Hit
A2:=FILTER(Performance!C2:C303, (Performance!J2:J303>=Performance!X2:X303)*(Performance!M2:M303>=Performance!Y2:Y303))

B1:Only Killpoints Hit
B2:=FILTER(Performance!C2:C303, (Performance!J2:J303>=Performance!X2:X303)*(Performance!M2:M303<Performance!Y2:Y303))

C1:Only Deaths Hit
C2:=FILTER(Performance!C2:C303, (Performance!J2:J303<Performance!X2:X303)*(Performance!M2:M303>=Performance!Y2:Y303))

D1:Nothing Hit
D2:=FILTER(Performance!C2:C303, (Performance!J2:J303<Performance!X2:X303)*(Performance!M2:M303<Performance!Y2:Y303))

##/End Target Status Sheet

##Dashboard Sheet:

A2:📈 Total Governors
B2:=COUNTA(Performance!B:B)-1

A3:⚡ Active Participants
B3:=COUNTIF(Performance!J:J,">0")

A4:⭐ Top Performers
B4:=COUNTIF(Performance!J:J,">=10000000")

A5:🏆 Elite Players
B5:=COUNTIF(Performance!J:J,">=10000000")

A6:💀 Total Killpoints
B6:=VALUE(SUM(Performance!J:J))

A7:⚰️ Total Deads
B7:=VALUE(SUM(Performance!M:M))

A8:📊 Avg KP per Player
B8:=VALUE(IF(B2>0,ROUND(B6/B2,0),0))

A9:🔥 Average Performance Ratio
B9:=VALUE(IFERROR(ROUND(AVERAGE(Performance!T:T),2),0))

A10:📋 Current Scan
B10:=Performance!W1

A11:🕐 Last Updated
B11:=NOW()

D2:=UNIQUE(FILTER(Performance!D:D,Performance!D:D<>""))

E2:Members
E3:=COUNTIF(Performance!D:D,D3)

F2:Total KP Gained
F3:=VALUE(SUMIF(Performance!D:D,D3,Performance!J:J))

##/End Dashboard Sheet

##Parameters Sheet:

A1:Power
A2:10,000,000
A3:20,000,000
A4:30,000,000
A5:40,000,000
A6:50,000,000
A7:60,000,000
A8:70,000,000
A9:80,000,000
A10:90,000,000
A11:100,000,000
A12:110,000,000
A13:120,000,000
A14:130,000,000
A15:140,000,000
A16:150,000,000
A17:160,000,000
A18:170,000,000
A19:180,000,000
A20:190,000,000
A21:200,000,000

B1:KP Ratio
B2:37.50%
B3:37.50%
B4:37.50%
B5:117.45%
B6:171.30%
B7:182.55%
B8:200.00%
B9:275.00%
B10:375.00%
B11:450.00%
B12:450.00%
B13:450.00%
B14:450.00%
B15:450.00%
B16:475.00%
B17:475.00%
B18:475.00%
B19:500.00%
B20:500.00%
B21:600.00%

C1:DP Ratio
C2:15.00%
C3:15.00%
C4:15.00%
C5:15.00%
C6:15.00%
C7:15.00%
C8:15.00%
C9:15.00%
C10:15.00%
C11:15.00%
C12:15.00%
C13:15.00%
C14:15.00%
C15:15.00%
C16:15.00%
C17:15.00%
C18:15.00%
C19:15.00%
C20:15.00%
C21:15.00%

##/End Parameters Sheet

##Contribution Sheet:

A1(ID):=QUERY(Performance!B:AO,"SELECT B,C,J,Z,AN,AO,(J+AN) WHERE B IS NOT NULL ORDER BY (J+AN) DESC LABEL B 'ID',C 'Nickname',J 'Total KP',Z 'KP Percent Achieved',AN 'Total Dead Points',AO 'DP Percent Achieved',(J+AN) 'Total Contribution'")
B1:Nickname
C1:Total KP
D1:KP Percent Achieved
E1:Total Percent Achieved
F1:DP Percent Achieved
G1:Total Contribution
H1:Contribution Rank

##/End Contribution Sheet

#Zeroed Sheet:

A1(ID):=QUERY(Performance!B:C,"SELECT B,C WHERE B IS NOT NULL ORDER BY C",1)
B1:Name
C1:Zeroed
C2:All the column is checkboxes

##//End Zeroed Sheet

##Penalties Sheet:

A1:Player ID
A2:=QUERY(Performance!B:C,"SELECT B,C WHERE B IS NOT NULL ORDER BY C",0)

B2:Player Name
C1:Penalty Active
C2:All the column down is checkboxes
D1:KP Penalty %
E1:Dead Penalty %
F1:Food Fine
G1:Wood Fine
H1:Stone Fine
I1:Gold Fine
J1:Reason/Notes
K1:Date Applied

##/End Penalties Sheet

##Hall Of Heroes Sheet:

A1(Player ID):=IMPORTRANGE("https://docs.google.com/spreadsheets/d/10uuE6FIXn3HkAry4Qy-CmwDdZwszCHCmyvSJQVHzvto/edit", "Hall of Heroes!A1:Z1000")
B1:T1 Infantry
C1:T1 Cavalry
D1:T1 Archers
E1:T1 Siege
F1:T2 Infantry
G1:T2 Cavalry
H1:T2 Archers
I1:T2 Siege
J1:T3 Infantry
K1:T3 Cavalry
L1:T3 Archers
M1:T3 Siege
N1:T4 Infantry
O1:T4 Cavalry
P1:T4 Archers
Q1:T4 Siege
R1:T5 Infantry
S1:T5 Cavalry
T1:T5 Archers
U1:T5 Siege


##/End Hall Of Heroes Sheet

##Account Links Sheet(Linked from google forms):

The google form responses submitted will be in this sheet based on those questions:

Main Account ID (Short answer, Required)
Main Account Name (Short answer, Required)
Farm Account 1 ID (Short answer, Required)
Farm Account 1 Name (Short answer, Required)
Farm Account 2 ID (Short answer, Optional)
Farm Account 2 Name (Short answer, Optional)
Farm Account 3 ID (Short answer, Optional)
Farm Account 3 Name (Short answer, Optional)
Farm Account 4 ID (Short answer, Optional)
Farm Account 4 Name (Short answer, Optional)
Farm Account 5 ID (Short answer, Optional)
Farm Account 5 Name (Short answer, Optional)

##/End Account Links Sheet

##Link Management Sheet

A1:Main_ID
A2:=ARRAYFORMULA(IF(LEN('Account Links'!B2:B)=0,"",'Account Links'!B2:B))

B1:Main_Name
B2:=ARRAYFORMULA(IF(LEN(A2:A)=0,"",IFERROR(VLOOKUP(A2:A,Performance!B:C,2,FALSE),"Name Not Found")))

C1:Main_ID_Status
C2:=ARRAYFORMULA(IF(LEN(A2:A)=0,"",IF(ISNUMBER(MATCH(A2:A,Performance!B:B,0)),"✅ Found","❌ Not Found")))

D1:Farm1_ID
D2:=ARRAYFORMULA(IF(LEN('Account Links'!D2:D)=0,"",'Account Links'!D2:D))

E1:Farm1_Name
E2:=ARRAYFORMULA(IF(LEN(D2:D)=0,"",IFERROR(VLOOKUP(D2:D,Performance!B:C,2,FALSE),"Name Not Found")))

F1:Farm1_Status
F2:=ARRAYFORMULA(IF(LEN(D2:D)=0,"",IF(ISNUMBER(MATCH(D2:D,Performance!B:B,0)),"✅ Found","❌ Not Found")))

G1:Farm2_ID
G2:=ARRAYFORMULA(IF(LEN('Account Links'!F2:F)=0,"",'Account Links'!F2:F))

H1:Farm2_Name
H2:=ARRAYFORMULA(IF(LEN(G2:G)=0,"",IFERROR(VLOOKUP(G2:G,Performance!B:C,2,FALSE),"Name Not Found")))

I1:Farm2_Status
I2:=ARRAYFORMULA(IF(LEN(G2:G)=0,"",IF(ISNUMBER(MATCH(G2:G,Performance!B:B,0)),"✅ Found","❌ Not Found")))

J1:Farm3_ID
J2:=ARRAYFORMULA(IF(LEN('Account Links'!H2:H)=0,"",'Account Links'!H2:H))

K1:Farm3_Name
K2:=ARRAYFORMULA(IF(LEN(J2:J)=0,"",IFERROR(VLOOKUP(J2:J,Performance!B:C,2,FALSE),"Name Not Found")))

L1:Farm3_Status
L2:=ARRAYFORMULA(IF(LEN(J2:J)=0,"",IF(ISNUMBER(MATCH(J2:J,Performance!B:B,0)),"✅ Found","❌ Not Found")))

M1:Farm4_ID
M2:=ARRAYFORMULA(IF(LEN('Account Links'!J2:J)=0,"",'Account Links'!J2:J))

N1:Farm4_Name
N2:=ARRAYFORMULA(IF(LEN(M2:M)=0,"",IFERROR(VLOOKUP(M2:M,Performance!B:C,2,FALSE),"Name Not Found")))

O1:Farm4_Status
O2:=ARRAYFORMULA(IF(LEN(M2:M)=0,"",IF(ISNUMBER(MATCH(M2:M,Performance!B:B,0)),"✅ Found","❌ Not Found")))

P1:Farm5_ID
P2:=ARRAYFORMULA(IF(LEN('Account Links'!L2:L)=0,"",'Account Links'!L2:L))

Q1:Farm5_Name
Q2:=ARRAYFORMULA(IF(LEN(P2:P)=0,"",IFERROR(VLOOKUP(P2:P,Performance!B:C,2,FALSE),"Name Not Found")))

R1:Farm5_Status
R2:=ARRAYFORMULA(IF(LEN(P2:P)=0,"",IF(ISNUMBER(MATCH(P2:P,Performance!B:B,0)),"✅ Found","❌ Not Found")))

S1:Overall_Status
S2:=ARRAYFORMULA(IF(LEN(A2:A)=0,"",
  IF(C2:C="❌ Not Found","❌ Invalid Main ID",
    IF(AND(
      OR(F2:F="✅ Found",F2:F=""),
      OR(I2:I="✅ Found",I2:I=""),
      OR(L2:L="✅ Found",L2:L=""),
      OR(O2:O="✅ Found",O2:O=""),
      OR(R2:R="✅ Found",R2:R="")
    ),"✅ All Valid","⚠️ Some Farm Issues")
  )
))

T1:Notes