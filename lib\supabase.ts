import { createClient } from '@supabase/supabase-js'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// For client components
export const createSupabaseClient = () => createClientComponentClient()

// Database helper functions
export class DatabaseService {
  private client = supabase

  // Players
  async getPlayers() {
    const { data, error } = await this.client
      .from('players')
      .select('*')
      .order('name')
    
    if (error) throw error
    return data
  }

  async upsertPlayer(player: { player_id: number; name: string; alliance?: string }) {
    const { data, error } = await this.client
      .from('players')
      .upsert(player, { onConflict: 'player_id' })
      .select()
    
    if (error) throw error
    return data[0]
  }

  // Scans
  async getScans() {
    const { data, error } = await this.client
      .from('scans')
      .select('*')
      .order('scan_number', { ascending: false })
    
    if (error) throw error
    return data
  }

  async getLatestScan() {
    const { data, error } = await this.client
      .from('scans')
      .select('*')
      .eq('is_latest', true)
      .single()
    
    if (error) throw error
    return data
  }

  async getBaselineScan() {
    const { data, error } = await this.client
      .from('scans')
      .select('*')
      .eq('is_baseline', true)
      .single()
    
    if (error) throw error
    return data
  }

  async createScan(scan: { scan_number: number; scan_name: string; is_baseline?: boolean }) {
    // First, unset any existing latest scan
    await this.client
      .from('scans')
      .update({ is_latest: false })
      .eq('is_latest', true)

    const { data, error } = await this.client
      .from('scans')
      .insert({
        ...scan,
        is_latest: true
      })
      .select()
    
    if (error) throw error
    return data[0]
  }

  // Player scan data
  async getPlayerScanData(scanId: string) {
    const { data, error } = await this.client
      .from('player_scan_data')
      .select(`
        *,
        players(name, alliance)
      `)
      .eq('scan_id', scanId)
      .order('power', { ascending: false })
    
    if (error) throw error
    return data
  }

  async upsertPlayerScanData(scanData: any[]) {
    const { data, error } = await this.client
      .from('player_scan_data')
      .upsert(scanData, { onConflict: 'scan_id,player_id' })
      .select()
    
    if (error) throw error
    return data
  }

  // Parameters
  async getParameters() {
    const { data, error } = await this.client
      .from('parameters')
      .select('*')
      .order('power_threshold')
    
    if (error) throw error
    return data
  }

  async upsertParameters(params: any[]) {
    const { data, error } = await this.client
      .from('parameters')
      .upsert(params, { onConflict: 'power_threshold' })
      .select()
    
    if (error) throw error
    return data
  }

  // Penalties
  async getPenalties() {
    const { data, error } = await this.client
      .from('penalties')
      .select(`
        *,
        players(name)
      `)
      .eq('is_active', true)
    
    if (error) throw error
    return data
  }

  async upsertPenalty(penalty: any) {
    const { data, error } = await this.client
      .from('penalties')
      .upsert(penalty, { onConflict: 'player_id' })
      .select()
    
    if (error) throw error
    return data[0]
  }

  // Zeroed players
  async getZeroedPlayers() {
    const { data, error } = await this.client
      .from('zeroed_players')
      .select(`
        *,
        players(name)
      `)
      .eq('is_zeroed', true)
    
    if (error) throw error
    return data
  }

  async upsertZeroedPlayer(zeroedData: any) {
    const { data, error } = await this.client
      .from('zeroed_players')
      .upsert(zeroedData, { onConflict: 'player_id' })
      .select()
    
    if (error) throw error
    return data[0]
  }

  // Hall of Heroes
  async getHallOfHeroes() {
    const { data, error } = await this.client
      .from('hall_of_heroes')
      .select(`
        *,
        players(name)
      `)
    
    if (error) throw error
    return data
  }

  async upsertHallOfHeroes(hohData: any[]) {
    const { data, error } = await this.client
      .from('hall_of_heroes')
      .upsert(hohData, { onConflict: 'player_id' })
      .select()
    
    if (error) throw error
    return data
  }
}
