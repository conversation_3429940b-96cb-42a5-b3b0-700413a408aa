'use client'

import React, { useState, useEffect } from 'react'
import { DashboardMetricsComponent } from '@/components/dashboard-metrics'
import { PerformanceTable } from '@/components/performance-table'
import { DatabaseService } from '@/lib/supabase'
import { PerformanceCalculator } from '@/lib/performance-calculator'
import { DashboardMetrics, PlayerPerformance } from '@/types/database'
import { Button } from '@/components/ui/button'
import { Upload, RefreshCw, BarChart3, Users, Settings } from 'lucide-react'
import Link from 'next/link'

export default function DashboardPage() {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null)
  const [performanceData, setPerformanceData] = useState<PlayerPerformance[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    setIsLoading(true)
    try {
      const db = new DatabaseService()
      
      // Get latest scan data
      const latestScan = await db.getLatestScan()
      const baselineScan = await db.getBaselineScan()
      
      if (!latestScan) {
        // No data available yet
        setIsLoading(false)
        return
      }

      // Get scan data
      const [currentScanData, baselineScanData, parameters, penalties, zeroedPlayers, hallOfHeroes] = await Promise.all([
        db.getPlayerScanData(latestScan.id),
        baselineScan ? db.getPlayerScanData(baselineScan.id) : Promise.resolve([]),
        db.getParameters(),
        db.getPenalties(),
        db.getZeroedPlayers(),
        db.getHallOfHeroes()
      ])

      // Calculate performance data
      const calculator = new PerformanceCalculator(parameters, penalties, zeroedPlayers, hallOfHeroes)
      
      const performance: PlayerPerformance[] = currentScanData.map((current, index) => {
        const baseline = baselineScanData.find(b => b.player_id === current.player_id)
        return calculator.calculatePlayerPerformance(current, baseline || null, index + 1)
      })

      // Sort by killpoints gain (descending)
      performance.sort((a, b) => b.killpoints_gain - a.killpoints_gain)
      
      // Update ranks
      performance.forEach((player, index) => {
        player.rank = index + 1
      })

      // Calculate dashboard metrics
      const dashboardMetrics: DashboardMetrics = {
        total_governors: performance.length,
        active_participants: performance.filter(p => p.killpoints_gain > 0).length,
        top_performers: performance.filter(p => p.killpoints_gain >= 5000000).length,
        elite_players: performance.filter(p => p.killpoints_gain >= 10000000).length,
        total_killpoints: performance.reduce((sum, p) => sum + p.killpoints_gain, 0),
        total_deads: performance.reduce((sum, p) => sum + p.deads_gain, 0),
        avg_kp_per_player: performance.length > 0 
          ? performance.reduce((sum, p) => sum + p.killpoints_gain, 0) / performance.length 
          : 0,
        avg_performance_ratio: calculateAverageRatio(performance),
        current_scan: latestScan.scan_name,
        last_updated: new Date().toISOString()
      }

      setMetrics(dashboardMetrics)
      setPerformanceData(performance)
      setLastRefresh(new Date())
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const calculateAverageRatio = (performance: PlayerPerformance[]): number => {
    const validRatios = performance
      .map(p => p.performance_ratio)
      .filter((ratio): ratio is number => typeof ratio === 'number' && isFinite(ratio))
    
    if (validRatios.length === 0) return 0
    return validRatios.reduce((sum, ratio) => sum + ratio, 0) / validRatios.length
  }

  const handleRefresh = () => {
    loadDashboardData()
  }

  if (isLoading && !metrics) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!metrics) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">No Data Available</h2>
          <p className="text-gray-600 mb-6">
            Get started by importing your first scan data to see the KVK dashboard.
          </p>
          <Link href="/import">
            <Button>
              <Upload className="h-4 w-4 mr-2" />
              Import Data
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">KVK Dashboard</h1>
              <p className="text-gray-600">
                Last updated: {lastRefresh.toLocaleTimeString()}
              </p>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Link href="/import">
                <Button>
                  <Upload className="h-4 w-4 mr-2" />
                  Import Data
                </Button>
              </Link>
              <Link href="/alliances">
                <Button variant="outline">
                  <Users className="h-4 w-4 mr-2" />
                  Alliances
                </Button>
              </Link>
              <Link href="/admin">
                <Button variant="outline">
                  <Settings className="h-4 w-4 mr-2" />
                  Admin
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Dashboard Metrics */}
          <DashboardMetricsComponent metrics={metrics} isLoading={isLoading} />

          {/* Performance Table */}
          <PerformanceTable data={performanceData} isLoading={isLoading} />
        </div>
      </div>
    </div>
  )
}
