// Database types for KVK Dashboard

export interface Player {
  id: string;
  player_id: number;
  name: string;
  alliance?: string;
  created_at: string;
  updated_at: string;
}

export interface Scan {
  id: string;
  scan_number: number;
  scan_name: string;
  is_baseline: boolean;
  is_latest: boolean;
  uploaded_at: string;
  uploaded_by?: string;
}

export interface PlayerScanData {
  id: string;
  scan_id: string;
  player_id: number;
  power: number;
  killpoints: number;
  deads: number;
  t1_kills: number;
  t2_kills: number;
  t3_kills: number;
  t4_kills: number;
  t5_kills: number;
  alliance?: string;
  created_at: string;
}

export interface Parameter {
  id: string;
  power_threshold: number;
  kp_ratio: number;
  dp_ratio: number;
  created_at: string;
}

export interface Penalty {
  id: string;
  player_id: number;
  is_active: boolean;
  kp_penalty_percent: number;
  dead_penalty_percent: number;
  food_fine: number;
  wood_fine: number;
  stone_fine: number;
  gold_fine: number;
  reason?: string;
  date_applied: string;
  applied_by?: string;
}

export interface ZeroedPlayer {
  id: string;
  player_id: number;
  is_zeroed: boolean;
  date_zeroed?: string;
  notes?: string;
  updated_by?: string;
  updated_at: string;
}

export interface HallOfHeroes {
  id: string;
  player_id: number;
  t1_infantry: number;
  t1_cavalry: number;
  t1_archers: number;
  t1_siege: number;
  t2_infantry: number;
  t2_cavalry: number;
  t2_archers: number;
  t2_siege: number;
  t3_infantry: number;
  t3_cavalry: number;
  t3_archers: number;
  t3_siege: number;
  t4_infantry: number;
  t4_cavalry: number;
  t4_archers: number;
  t4_siege: number;
  t5_infantry: number;
  t5_cavalry: number;
  t5_archers: number;
  t5_siege: number;
  last_updated: string;
}

export interface AccountLink {
  id: string;
  main_account_id: number;
  main_account_name: string;
  farm1_id?: number;
  farm1_name?: string;
  farm2_id?: number;
  farm2_name?: string;
  farm3_id?: number;
  farm3_name?: string;
  farm4_id?: number;
  farm4_name?: string;
  farm5_id?: number;
  farm5_name?: string;
  submitted_at: string;
}

// Computed performance data types
export interface PlayerPerformance {
  rank: number;
  player_id: number;
  name: string;
  alliance?: string;
  power_baseline: number;
  power_current: number;
  power_change: number;
  killpoints_baseline: number;
  killpoints_current: number;
  killpoints_gain: number;
  deads_baseline: number;
  deads_current: number;
  deads_gain: number;
  total_kills_gain: number;
  performance_ratio: number | string;
  status: string;
  efficiency_score: number;
  kp_target: number;
  deads_target: number;
  kp_percent_achieved: number;
  deads_percent_achieved: number;
  is_new_player: boolean;
  is_zeroed: boolean;
  is_penalized: boolean;
  total_t4_deads: number;
  total_t5_deads: number;
  dead_points_earned: number;
}

// Dashboard metrics
export interface DashboardMetrics {
  total_governors: number;
  active_participants: number;
  top_performers: number;
  elite_players: number;
  total_killpoints: number;
  total_deads: number;
  avg_kp_per_player: number;
  avg_performance_ratio: number;
  current_scan: string;
  last_updated: string;
}

// Alliance performance
export interface AlliancePerformance {
  alliance: string;
  members: number;
  total_kp_gained: number;
  avg_kp_per_member: number;
  total_deads: number;
  performance_ratio: number;
}

// File upload types
export interface UploadedFile {
  file: File;
  type: 'excel' | 'csv' | 'json';
  preview?: any[];
}

export interface ImportResult {
  success: boolean;
  message: string;
  imported_count?: number;
  errors?: string[];
}
